#!/usr/bin/env python3
"""
测试配置优化效果的脚本
"""
import time
from unittest.mock import patch, MagicMock

def test_config_optimization():
    """测试配置优化前后的性能差异"""
    print("=== 配置优化效果测试 ===\n")
    
    # 模拟原始的重复调用方式
    print("1. 模拟原始方式（每次都查询配置中心）:")
    
    mock_config_loader = MagicMock()
    mock_config_loader.get_application_config_by_key.return_value = "true"
    
    def original_use_sdk_for_prediction(data_length, is_encrypt, pred_col):
        # 原始方式：每次都调用配置中心
        escape_switch_enabled = mock_config_loader.get_application_config_by_key("escape_switch_enabled") == 'true'
        if escape_switch_enabled:
            return True
        return data_length < 11 or pred_col == 'equip_rev_cons_after_amt' or is_encrypt
    
    # 测试原始方式的性能
    start_time = time.time()
    call_count = 0
    for i in range(1000):  # 模拟1000次调用
        result = original_use_sdk_for_prediction(20, False, "test_col")
        call_count += 1
    
    original_time = time.time() - start_time
    config_query_count = mock_config_loader.get_application_config_by_key.call_count
    
    print(f"   调用次数: {call_count}")
    print(f"   配置查询次数: {config_query_count}")
    print(f"   总耗时: {original_time:.4f}秒")
    print(f"   平均每次耗时: {original_time/call_count*1000:.4f}毫秒")
    print()
    
    # 模拟优化后的缓存方式
    print("2. 模拟优化后方式（使用缓存）:")
    
    class MockConfigSwitches:
        def __init__(self):
            # 只在初始化时查询一次
            self.escape_switch_enabled = mock_config_loader.get_application_config_by_key("escape_switch_enabled") == 'true'
    
    # 重置mock计数器
    mock_config_loader.reset_mock()
    mock_config_loader.get_application_config_by_key.return_value = "true"
    
    # 创建缓存实例（只查询一次）
    config_switches = MockConfigSwitches()
    
    def optimized_use_sdk_for_prediction(data_length, is_encrypt, pred_col):
        # 优化方式：使用缓存的配置
        if config_switches.escape_switch_enabled:
            return True
        return data_length < 11 or pred_col == 'equip_rev_cons_after_amt' or is_encrypt
    
    # 测试优化后的性能
    start_time = time.time()
    call_count = 0
    for i in range(1000):  # 模拟1000次调用
        result = optimized_use_sdk_for_prediction(20, False, "test_col")
        call_count += 1
    
    optimized_time = time.time() - start_time
    config_query_count_optimized = mock_config_loader.get_application_config_by_key.call_count
    
    print(f"   调用次数: {call_count}")
    print(f"   配置查询次数: {config_query_count_optimized}")
    print(f"   总耗时: {optimized_time:.4f}秒")
    print(f"   平均每次耗时: {optimized_time/call_count*1000:.4f}毫秒")
    print()
    
    # 性能对比
    print("3. 性能对比:")
    print(f"   配置查询次数减少: {config_query_count - config_query_count_optimized} 次")
    print(f"   耗时减少: {original_time - optimized_time:.4f}秒")
    if original_time > 0:
        improvement = ((original_time - optimized_time) / original_time) * 100
        print(f"   性能提升: {improvement:.2f}%")
    print()
    
    # 实际场景分析
    print("4. 实际场景分析:")
    print("   在实际应用中，如果 use_sdk_for_prediction 函数被频繁调用：")
    print("   - 每次预测任务可能调用数百次")
    print("   - 每次配置查询涉及网络请求和数据处理")
    print("   - 使用缓存可以显著减少网络开销和CPU使用")
    print("   - 特别是在高并发场景下，效果更明显")


def test_cache_consistency():
    """测试缓存一致性"""
    print("\n=== 缓存一致性测试 ===\n")
    
    print("1. 测试配置开关缓存的一致性:")
    
    # 模拟配置变更场景
    mock_config_loader = MagicMock()
    
    class MockConfigSwitches:
        def __init__(self):
            self._load_switches()
        
        def _load_switches(self):
            self.escape_switch_enabled = mock_config_loader.get_application_config_by_key("escape_switch_enabled") == 'true'
        
        def reload_switches(self):
            print("   重新加载配置开关...")
            self._load_switches()
    
    # 初始配置为 true
    mock_config_loader.get_application_config_by_key.return_value = "true"
    config_switches = MockConfigSwitches()
    print(f"   初始配置: escape_switch_enabled = {config_switches.escape_switch_enabled}")
    
    # 模拟配置中心配置变更为 false
    mock_config_loader.get_application_config_by_key.return_value = "false"
    print("   配置中心配置已变更为 false")
    print(f"   缓存中的配置: escape_switch_enabled = {config_switches.escape_switch_enabled}")
    
    # 重新加载配置
    config_switches.reload_switches()
    print(f"   重新加载后的配置: escape_switch_enabled = {config_switches.escape_switch_enabled}")
    
    print("\n2. 缓存更新建议:")
    print("   - 对于开关类配置，通常在应用启动时加载一次即可")
    print("   - 如需运行时更新，可以提供 reload_switches() 方法")
    print("   - 也可以考虑定时刷新或通过管理接口手动刷新")


if __name__ == "__main__":
    test_config_optimization()
    test_cache_consistency()
    print("\n=== 测试完成 ===")
