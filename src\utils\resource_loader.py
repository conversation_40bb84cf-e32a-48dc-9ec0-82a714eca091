import copy
import json
import os

from pyxis.utils.config_loader import Config<PERSON><PERSON><PERSON>


def read_config() -> dict:
    env = os.environ.get('ENV', 'LOCAL')
    config_path = os.path.join("src", "config", f"{env}.json")

    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config = json.load(f)
    except FileNotFoundError as e:
        raise ValueError(f"环境 '{env}' 的配置文件不存在: {config_path}") from e
    except json.JSONDecodeError as e:
        raise ValueError(f"配置文件格式错误: {config_path}") from e
    return config


CONFIG_LOADER = ConfigLoader(read_config())
CONFIG = copy.deepcopy(CONFIG_LOADER.config)
RDB_POOLS = CONFIG_LOADER.get_rdb_pools()
RDB_POOL = RDB_POOLS['default']
LOGGER = CONFIG_LOADER.get_logger()
