import io
import os
import time
from io import BytesIO
from multiprocessing import Pool
from pathlib import Path
from typing import List, Union, Tuple, Any, Dict
from urllib.parse import urlparse

import numpy as np
import pandas as pd
import requests
from AutoTsPred.experiment.profit_pipeline import rule_profit_pipeline

from pyxis.utils.authorization.his_authorization import get_dynamic_token_without_param
from src.time_series_pred.common.dimension_constant import ESCAPE_SWITCH_ENABLED
from src.time_series_pred.services.conditional_line_trend_judge import conditional_line_trend
from src.time_series_pred.services.profit_exception_pipeline import check_pipeline, reference_pipline_trend
from src.utils.resource_loader import LOGGER as log, CONFIG

# 调用api预测传参常量 - 延迟初始化缓存
_ifs_api_params_cache = {}

dim_need_interval_prediction = ['unit_cost', 'unit_price']
nondim_need_interval_prediction = ['mgp_ratio']
lv2nodim_need_interval_prediction = ['mgp_ratio_after', 'equip_rev_cons_after_amt']
indicators_need_interval_prediction = dim_need_interval_prediction + nondim_need_interval_prediction + lv2nodim_need_interval_prediction


def months_until_next_year_end(pred_version):
    """
    计算从当前期到同年12月的月份个数
    """
    pred_version = str(pred_version)
    pred_month = int(pred_version[-2:])
    # 计算当前月份到12月的月份个数
    if pred_month < 1 or pred_month > 12:
        raise ValueError("月份必须在 1 到 12 之间")
    #
    if pred_month in [11, 12]:
        months_count = 12 - pred_month + 13
    else:
        months_count = 12 - pred_month + 1

    return months_count


def get_params_for_algo_sdk(target_name: str, pred_version: int):
    # 不同指标的评估函数、变点检测参数不同
    if target_name in ["unit_cost", "unit_price"]:
        metric = "mape"
    elif target_name in ["equip_rev_cons_after_amt"]:
        metric = "mape"
    else:
        metric = "mae"
    # 是否需要做变点检测，当吧bayes_p_value>1，即无论如何都不会检测出变点，这里用于表示是否需要变点检测，下面这些指标不需要做变点检测
    if target_name in ["mca_adjust_ratio", "mgp_adjust_ratio", "unit_price", "unit_cost",
                        "rev_percent", "carryover_rate"]:
        bayes_p_value = 1.1
    elif target_name in ["equip_rev_cons_after_amt"]:
        bayes_p_value = 0.1
    else:
        bayes_p_value = 0.01
    # 不同指标、使用的模型不同
    if target_name in ["unit_price"]:
        model_nums = ["auto_ets", "prophet", "naive"]
    else:
        model_nums = ["arima", "auto_ets", "prophet", "naive"]
    # 不同指标模型参数不同，autoarima的季节性参数都是False
    seasonal = False
    # 不同指标模型参数不同，这里的参数用于调整，YTD法预测结果
    if target_name in ["equip_rev_cons_after_amt"]:
        adjust_tag = True
    else:
        adjust_tag = False
    # 不同指标模型参数不同, 这里调节是否需要异常检测，线性外推检测参数
    # "carryover_rate"
    if target_name in ["mca_adjust_ratio", "mgp_adjust_ratio"]:
        anomaly_entry = "not_bayes_cycle_test"  # 不做异常检测
        trend_thold = 0.65  # 线性外推参数
    elif target_name in ["equip_rev_cons_after_amt"]:
        anomaly_entry = "not_bayes_cycle_test"
        trend_thold = 1.1  # 大于1不会检测出线性外推趋势
    else:
        anomaly_entry = "bayes_cycle_test"  # 做异常检测
        trend_thold = 0.8  # 线性外推参数
    ignore_month = [11, 12]  # ytd法
    if target_name in ["carryover_rate"]:
        anomaly_model = "box"
    else:
        anomaly_model = "three_sigma"
    steps = months_until_next_year_end(pred_version)
    param_set = (anomaly_entry, anomaly_model, ignore_month, trend_thold, adjust_tag,
                 bayes_p_value, model_nums, metric, seasonal, steps)
    params = get_params_for_sdk(param_set)
    return params


# for sdk
def get_params_for_sdk(param_set):
    (anomaly_entry, anomaly_model, ignore_month, trend_thold, adjust_tag, bayes_p_value,
     model_nums, metric, seasonal, steps) = param_set
    params = {
        # 异常检测
        "anomaly_entry_conditions": anomaly_entry,
        "anomaly_model_name": anomaly_model,
        "anomaly_kwargs": {
            "thold": 4,
            "post_process_method": "ignore_anomaly_by_month",  # 指定异常检测后处理方法
            "ignore_month_list": ignore_month
        },  # 指定要忽略的异常月份
        "reference_detection": True,
        "line_trend_thres": trend_thold,
        "adjust_tag": adjust_tag,
        # 变点检测
        "bayes_p_value": bayes_p_value,
        "is_truncated": True,
        "ignore_last_n": 13,  # 截断数据时忽略最后n个时点中识别出的变点
        # 短时序
        "short_ts_threshold": 5,  # 短时序判断阈值，大于阈值就不是短时序
        # 数据集划分 - 暂时写死
        "fixed_test_size": 6,
        "models": model_nums,
        "eval_metric": metric,  # 点预测评估指标
        "confidence": 0.95,
        "ensemble_method": "weight",
        "plot": None,
        "model_params": {
            "arima": {
                "seasonal": seasonal
            },
            "naive": {
                "sp": 12
            },
            "auto_ets": {
                "seasonal_periods": 12,
                "params": {
                    "yearly_accumulation": True
                }
            },
            "prophet": {},
        },
        "steps": steps,
        "predict_rule": "fill_data"
    }
    return params


def params_for_crd(target_name):
    """
    条件可参考性判断的参数
    """
    # 不同指标的评估函数、条件可参考性参数不同
    if target_name in ["unit_cost", "unit_price"]:
        bayes_p_value = 0.05
    elif target_name in ["equip_rev_cons_after_amt"]:
        bayes_p_value = 0.1
    else:
        bayes_p_value = 0.01
    # 是否需要做条件可参考性检测，当吧bayes_p_value>1，即无论如何都不会检测出变点，这里用于表示是否需要变点检测，下面这些指标不需要做变点检测
    if target_name in ["mca_adjust_ratio",
                       "mgp_adjust_ratio",
                       "unit_price",
                       "unit_cost",
                       "carryover_rate",
                       "rev_percent"]:
        bayes_p_value = 1.1
    params = {
        # 可参考性检测
        "reference_detection": True,
        # 变点检测
        "bayes_p_value": bayes_p_value,  # 贝叶斯变点检测p值
        "is_truncated": True,  # 是否按变点截断数据
        "ignore_last_n": 13,  # 截断数据时忽略最后n个时点中识别出的变点
    }
    return params


def params_for_line_trend(data_col):
    if data_col in ["mca_adjust_ratio", "mgp_adjust_ratio", "carryover_rate"]:
        trend_thold = 0.65  # 线性外推参数
    elif data_col in ["equip_rev_cons_after_amt"]:
        trend_thold = 1.1  # 大于1不会检测出线性外推趋势
    else:
        trend_thold = 0.8  # 线性外推参数
    return trend_thold


def params_for_cad(target_name):
    """
    包括：异常检测、数据可参考性检测、变点检测
    :param target_name: 预测参数名
    :return:
    """
    # 不同指标的评估函数、变点检测参数不同
    if target_name in ["unit_cost", "unit_price"]:
        bayes_p_value = 0.05
    elif target_name in ["equip_rev_cons_after_amt"]:
        bayes_p_value = 0.1
    else:
        bayes_p_value = 0.01

    # 是否需要做变点检测，当吧bayes_p_value>1，即无论如何都不会检测出变点，这里用于表示是否需要变点检测，下面这些指标不需要做变点检测
    if target_name in ["mca_adjust_ratio",
                       "mgp_adjust_ratio",
                       "unit_price",
                       "unit_cost",
                       "carryover_rate",
                       "rev_percent"]:
        bayes_p_value = 1.1

    # 不同指标模型参数不同，这里的参数用于调整，YTD法预测结果
    if target_name in ["equip_rev_cons_after_amt"]:
        adjust_tag = True
    else:
        adjust_tag = False

    # 不同指标模型参数不同, 这里调节是否需要异常检测
    # "carryover_rate"
    if target_name in ["equip_rev_cons_after_amt",
                       "mca_adjust_ratio",
                       "mgp_adjust_ratio",
                       ]:
        anomaly_entry = "not_bayes_cycle_test"  # 不做异常检测
    else:
        anomaly_entry = "bayes_cycle_test"  # 做异常检测
    ignore_month = [11, 12]  # ytd法
    if target_name in ["carryover_rate"]:
        anomaly_model = "box"
    else:
        anomaly_model = "three_sigma"
    params = {
        # 异常检测
        "anomaly_entry_conditions": anomaly_entry,  # True,
        "anomaly_model_name": anomaly_model,
        "anomaly_kwargs": {
            "thold": 4,
            "post_process_method": "ignore_anomaly_by_month",  # 指定异常检测后处理方法
            "ignore_month_list": ignore_month,  # 指定要忽略的异常月份
        },
        "reference_detection": True,
        # 变点检测
        "bayes_p_value": bayes_p_value,  # 贝叶斯变点检测p值
        "is_truncated": True,  # 是否按变点截断数据
        "ignore_last_n": 13,  # 截断数据时忽略最后n个时点中识别出的变点
    }
    return params


# 已废弃
def process_row(
        df: pd.DataFrame,
        period_col: str,
        data_col: str,
        image_name: Union[None, str],
        pred_version: int
) -> Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, dict, str]:
    """
    处理单个维度+预测col=预测对象的时序
    处理成Time,Data,UNION_ATTR,没有Miss,有ytd_data
    预测返回5种数据类型
    """
    if df.empty:
        empty_df = pd.DataFrame(columns=['Time', 'y_hat'])
        return empty_df, empty_df, np.array([]), {}, "Missing dimensions"
    # 处理成Time,Data,UNION_ATTR, 没有：Miss,如果线性外推趋势，如果是那几个指标需要有：ytd_data
    df['Time'] = df[period_col].apply(
        lambda x: f"{str(x)[:4]}-{str(x)[4:6]}" if str(x).endswith('YTD') else str(x)[:-3])
    df['UNION_ATTR'] = image_name
    df['Data'] = df[data_col]
    # 向前填充空值,其余空值置0
    df.sort_values(by='Time', inplace=True)
    df['Data'] = df['Data'].fillna(method='ffill')
    df['Data'] = pd.to_numeric(df['Data'], errors='coerce')
    df['Data'] = df['Data'].fillna(0)
    params = get_params_for_algo_sdk(data_col, pred_version)
    if data_col in ['unit_cost', 'unit_price', 'carryover_rate', "mca_adjust_ratio", "mgp_adjust_ratio"]: # 可能做规则预测
        if data_col + '_ytd_data' in list(df.columns):
            df['ytd_data'] = df[data_col + '_ytd_data']
            if not (df['ytd_data'].empty or df['ytd_data'].isna().all()):
                _df = df[['Time', 'Data', 'ytd_data', 'UNION_ATTR']].reset_index(drop=True)
            else:
                _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
        else:
            _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    else:
        _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    a, b, c, d, e = rule_profit_pipeline(_df, params)
    return a, b, c, d, e


def tspred(df: pd.DataFrame,
           target_name: str, pred_version: int, image_name: Union[None, str]) -> pd.DataFrame:
    """
    时序预测API调用函数

    Args:
        df: 输入数据DataFrame
        target_name: 预测目标名称
        pred_version: 预测版本
        image_name: 维度标识名称

    Returns:
        predict_df: 预测结果DataFrame
    """
    tspred_start_time = time.time()
    log.info(f"[TSPRED开始] 指标: {target_name}, 维度: {image_name}, 输入数据shape: {df.shape}")

    def get_api_params(data_col: str) -> int:
        # 均本-损益收入(还有其他几个):  problemId：585942，taskId：585942，solutionId：11306
        # 均价:         problemId：585942，taskId：585942，solutionId：11308
        # 结转率:        problemId：585942，taskId：585942，solutionId：11310
        api_config = get_ifs_api_params()
        unit_cost_rev_col = ['unit_cost', 'equip_rev_cons_after_amt']
        unit_price_col = ['unit_price']
        other_col = ["carryover_rate", "rev_percent", "mca_adjust_ratio", "mgp_adjust_ratio",
                     'mgp_ratio_after', 'mgp_ratio']
        solutionId = None
        if data_col in unit_cost_rev_col:
            solutionId = api_config['unit_cost_solutionId']
        elif data_col in unit_price_col:
            solutionId = api_config['unit_price_solutionId']
        elif data_col in other_col:  # 对应其他预测参数
            solutionId = api_config['other_param_solutionId']

        if solutionId is None:
            raise Exception(f'solutionId参数获取错误，无{target_name}对应的solutionId参数')
        return solutionId


    def Upload_Predict_getVersionId(url: str,
                                    trainSet: bytes,
                                    testSet: bytes,
                                    ) -> int:
        upload_start = time.time()
        api_params = get_ifs_api_params()
        log.info(f"[上传请求] 开始上传预测文件, problemId: {api_params['problemId']}, solutionId: {solutionId}")

        request_headers = {
            'Authorization': get_dynamic_token_without_param(),
            "Referer": CONFIG.forcast_api.referer
        }
        body = {
            "problemId": api_params['problemId'],
            "solutionId": solutionId,
            "testTargetFlag": "N",
            "modelFlag": "N",
            "taskId": api_params['taskId']
        }
        files = {
            "trainSet": ("train.csv", trainSet),
            "testSet": ("test.csv", testSet),
        }

        versionId = None
        try:
            response = requests.post(url, headers=request_headers, data=body, files=files,
                                     timeout=CONFIG.forcast_api.request_max_timeout, verify=False)
            upload_time = time.time() - upload_start

            if response.status_code == 200:
                result = response.json()
                versionId = result.get('data')
                log.info(f"[上传成功] versionId: {versionId}, 耗时: {upload_time:.2f}秒")
            else:
                log.error(
                    f"[上传失败] 状态码: {response.status_code}, problemId: {api_params['problemId']}, 耗时: {upload_time:.2f}秒")
                log.error(f"[上传失败详情] 响应内容: {response.text}")
                raise requests.exceptions.HTTPError()
        except Exception as e:
            upload_time = time.time() - upload_start
            log.error(f"[上传异常] 异常: {str(e)}, problemId: {api_params['problemId']}, 耗时: {upload_time:.2f}秒")
            raise e

        return versionId


    def get_indicator_forecast(url, problemId, solutionId, version_id, dynamic_token):
        """
        获取预测结果api调用
        """
        request_start = time.time()

        request_headers = {
            'Authorization': dynamic_token,
            "Referer": CONFIG.forcast_api.referer
        }
        params = {
            "problemId": problemId,
            "solutionId": solutionId,
            "versionId": version_id,
        }
        data = None
        response = None

        try:
            response = requests.get(url, params=params, headers=request_headers, timeout=CONFIG.forcast_api.request_max_timeout, verify=False)
            request_time = time.time() - request_start

            if response.status_code == 200:
                result = response.json()
                if result:
                    http_code = result.get("httpCode")
                    if http_code == 200:
                        data = result.get("data")
                        log.info(f"[获取预测URL成功] versionId: {version_id}, 耗时: {request_time:.2f}秒")
                    else:
                        log.warning(f"[获取预测URL] versionId: {version_id}, httpCode: {http_code}, 耗时: {request_time:.2f}秒")
                else:
                    log.info(f"[预测结果为空] versionId: {version_id}, 耗时: {request_time:.2f}秒")
            elif response.status_code == 500:  # 如果500直接抛出异常不再轮询
                log.error(f"[服务器错误] versionId: {version_id}, 状态码: 500, 耗时: {request_time:.2f}秒")
                raise requests.exceptions.HTTPError()
            else:
                log.error(f"[获取预测URL失败] versionId: {version_id}, 状态码: {response.status_code}, 耗时: {request_time:.2f}秒")
                log.error(f"[响应详情] 响应内容: {response.text}")
                log.error(f"[请求参数] problemId: {problemId}, solutionId: {solutionId}, versionId: {version_id}")
        except Exception as e:
            request_time = time.time() - request_start
            log.error(f"[请求异常] versionId: {version_id}, 异常: {str(e)}, 耗时: {request_time:.2f}秒")
            log.error(f"[异常请求参数] problemId: {problemId}, solutionId: {solutionId}, versionId: {version_id}")
            if response and response.status_code == 500:
                raise e
        return data


    def get_result_url(url, versionId):
        max_duration = CONFIG.forcast_api.max_duration
        interval = CONFIG.forcast_api.interval
        start_time = time.time()
        end_time = start_time + max_duration
        dynamic_token = get_dynamic_token_without_param()
        res_url = None
        remaining = None
        poll_count = 0

        log.info(f"[轮询开始] versionId: {versionId}, 最大轮询时间: {max_duration}秒, 轮询间隔: {interval}秒")

        while time.time() < end_time:
            try:
                poll_count += 1
                # 计算剩余时间并动态调整休眠时间
                remaining = end_time - time.time()
                sleep_time = min(interval, remaining)

                log.info(f"[轮询进行中] versionId: {versionId}, 第{poll_count}次轮询, 剩余时间: {remaining:.1f}秒")
                time.sleep(sleep_time)

                poll_start = time.time()
                api_params = get_ifs_api_params()
                res_url = get_indicator_forecast(url, api_params['problemId'], solutionId, versionId, dynamic_token)
                poll_duration = time.time() - poll_start

                if res_url:
                    total_time = time.time() - start_time
                    log.info(f"[轮询成功] versionId: {versionId}, 第{poll_count}次轮询成功, 单次耗时: {poll_duration:.2f}秒, 总耗时: {total_time:.2f}秒")
                    return res_url
                else:
                    log.info(f"[轮询等待] versionId: {versionId}, 第{poll_count}次轮询未获取到结果, 单次耗时: {poll_duration:.2f}秒, 继续等待...")

            except Exception as e:
                error_time = time.time() - start_time
                api_params = get_ifs_api_params()
                log.error(f"[轮询异常] versionId: {versionId}, 第{poll_count}次轮询异常: {str(e)}, 耗时: {error_time:.2f}秒")
                log.error(f"[轮询异常详情] problemId: {api_params['problemId']}, solutionId: {solutionId}, versionId: {versionId}")
                return None

        # 轮询超时
        consum_time = max_duration - (remaining if remaining else 0)
        api_params = get_ifs_api_params()
        if not res_url:
            log.error(f'[轮询超时] versionId: {versionId}, problemId: {api_params["problemId"]}, 总轮询次数: {poll_count}, 耗时: {consum_time:.2f}秒')
        else:
            log.info(f'[轮询完成] versionId: {versionId}, problemId: {api_params["problemId"]}, 总轮询次数: {poll_count}, 耗时: {consum_time:.2f}秒')
        return res_url


    def df_to_csv_buffer(df_train: pd.DataFrame, pred_col, image_name: Union[None, str]) -> Tuple[bytes, bytes]:
        """
        数据处理函数
        参数:
            df: 原始数据DataFrame
            target_name: 目标列名
        返回:
            tuple: (train_data_csv_bytes, test_data_csv_bytes)
        """
        df_test = pd.DataFrame(columns=df_train.columns)
        df_train["Time"] = pd.to_datetime(df_train["Time"], format="%Y-%m")
        max_date = df_train['Time'].max() + pd.DateOffset(months=1)
        new_dates = pd.date_range(start=max_date, periods=steps, freq='MS')
        df_test['Time'] = new_dates
        df_train.sort_values(by="Time", inplace=True)
        df_test.sort_values(by="Time", inplace=True)
        df_train.drop_duplicates(['Time'], inplace=True)
        df_test.drop_duplicates(['Time'], inplace=True)
        df_train.rename(columns={target_name: "Data"}, inplace=True)
        df_test.rename(columns={target_name: "Data"}, inplace=True)
        df_train.reset_index(drop=True, inplace=True)
        df_test.reset_index(drop=True, inplace=True)
        mon_continue = is_monthly_continuous(df_train,df_test)  # 考虑到耗时时可以去掉该判断
        if not mon_continue:
            log.error("训练集测试集是否是否连续：{},预测参数：{}".format(mon_continue, pred_col))
            log.error("image_name:{}".format(image_name))
            log.error("train_Time:{},Test_time{}".format(df_train['Time'].tolist(), df_test['Time'].tolist()))
        if len(df) != len(df_train):
            log.error("训练集存在重复数据：{},预测参数：{}".format(mon_continue, pred_col))
            log.error("image_name:{}".format(image_name))
            log.error("train_Time:{},Test_time{}".format(df_train['Time'].tolist(), df_test['Time'].tolist()))
        csv_buffer_train = BytesIO()
        df_train.to_csv(csv_buffer_train, index=False)
        csv_buffer_train.seek(0)  # 将指针重置到文件开头

        csv_buffer_test = BytesIO()
        df_test.to_csv(csv_buffer_test, index=False)
        csv_buffer_test.seek(0)  # 将指针重置到文件开头
        return csv_buffer_train, csv_buffer_test

    # 1. 配置读取和参数准备
    config_start_time = time.time()

    solutionId = get_api_params(target_name)
    steps = months_until_next_year_end(pred_version)
    log.info(f"[配置准备完成] 指标: {target_name}, 维度: {image_name}, solutionId: {solutionId}, steps: {steps}, 耗时: {time.time() - config_start_time:.2f}秒")

    # 2. 数据处理和上传
    data_prep_start_time = time.time()
    csv_buffer_train, csv_buffer_test = df_to_csv_buffer(df, pred_col=target_name, image_name=image_name)
    log.info(f"[数据处理完成] 指标: {target_name}, 维度: {image_name}, 耗时: {time.time() - data_prep_start_time:.2f}秒")

    # 3. 上传预测文件
    upload_start_time = time.time()
    log.info(f"[开始上传] 指标: {target_name}, 维度: {image_name}")
    versionId = Upload_Predict_getVersionId(url=CONFIG.forcast_api.upload_url, trainSet=csv_buffer_train, testSet=csv_buffer_test)
    log.info(f"[上传完成] 指标: {target_name}, 维度: {image_name}, versionId: {versionId}, 耗时: {time.time() - upload_start_time:.2f}秒")

    # 4. 获取预测结果
    polling_start_time = time.time()
    log.info(f"[开始轮询] 指标: {target_name}, 维度: {image_name}, versionId: {versionId}")
    result_url = get_result_url(CONFIG.forcast_api.forecast_result_url, versionId)
    log.info(f"[轮询完成] 指标: {target_name}, 维度: {image_name}, versionId: {versionId}, 轮询耗时: {time.time() - polling_start_time:.2f}秒")

    # 5. 下载预测文件
    download_start_time = time.time()
    log.info(f"[开始下载] 指标: {target_name}, 维度: {image_name}, result_url: {result_url}")
    # 校验下载链接域名
    allowed_hosts = {"s3-kp-kwe.his-beta.huawei.com", "s3-hc-dgg.hics.huawei.com"}
    if not result_url or not isinstance(result_url, str):
        log.error(f"[下载链接无效] 指标: {target_name}, 维度: {image_name}, versionId: {versionId}, result_url: {result_url}")
        raise requests.exceptions.InvalidURL("result_url 无效")
    parsed_url = urlparse(result_url.strip())
    if parsed_url.scheme not in ("http", "https") or not parsed_url.hostname:
        log.error(f"[下载链接格式错误] 指标: {target_name}, 维度: {image_name}, url: {result_url}")
        raise requests.exceptions.InvalidURL("result_url 格式错误")
    if parsed_url.hostname not in allowed_hosts:
        log.error(f"[下载域名不允许] 指标: {target_name}, 维度: {image_name}, host: {parsed_url.hostname}, 允许: {allowed_hosts}")
        raise requests.exceptions.InvalidURL(f"下载域名不允许: {parsed_url.hostname}")
    file_data = requests.get(result_url, timeout=CONFIG.forcast_api.request_max_timeout, verify=False)
    predict_df = pd.read_csv(io.StringIO(file_data.text))
    log.info(f"[下载完成] 指标: {target_name}, 维度: {image_name}, 预测结果shape: {predict_df.shape}, 下载耗时: {time.time() - download_start_time:.2f}秒")

    # 6. 结果验证
    if predict_df.empty:
        log.error(f"[预测结果为空] 指标: {target_name}, 维度: {image_name}, versionId: {versionId}, solutionId: {solutionId}, 预测文件shape: {predict_df.shape}")
    else:
        log.info(f"[预测结果验证] 指标: {target_name}, 维度: {image_name}, 预测结果行数: {len(predict_df)}")

    total_tspred_time = time.time() - tspred_start_time
    log.info(f"[TSPRED完成] 指标: {target_name}, 维度: {image_name}, 总耗时: {total_tspred_time:.2f}秒")

    return predict_df


def process_data(df: pd.DataFrame, period_col: str, data_col: str,
                 metric_name: Union[None, str]):
    '''
    处理数据
    :param df:
    :param period_col:
    :param data_col:
    :param metric_name:
    :return:
    '''

    # 处理成Time,Data,UNION_ATTR,
    # 没有：Miss,
    # 如果线性外推趋势，如果是那几个指标需要有：ytd_data
    df['Time'] = df[period_col].apply(
        lambda x: f"{str(x)[:4]}-{str(x)[4:6]}" if str(x).endswith('YTD') else str(x)[:-3])
    # 维度指标名称
    df['UNION_ATTR'] = metric_name
    df['Data'] = df[data_col]
    # 向前填充空值,其余空值置0
    df.sort_values(by='Time', inplace=True)
    df['Data'] = df['Data'].fillna(method='ffill')
    df['Data'] = pd.to_numeric(df['Data'], errors='coerce')
    df['Data'] = df['Data'].fillna(0)
    # 获取填充值
    fill_value = df['Data'].iloc[-1]

    _df = ytd_add(df, data_col)
    return _df, fill_value

# 截取df 数据， 如果 YTD 列有有效数据（非全空），则 _df 包含四列， 否则包含三列
def ytd_add(df, data_col):
    if data_col in ('unit_cost', 'unit_price', 'carryover_rate', "mca_adjust_ratio", "mgp_adjust_ratio"):  # 可能做规则预测
        if data_col + '_ytd_data' in list(df.columns): # 判断 data_col 是否属于有YTD 数据
            df['ytd_data'] = df[data_col + '_ytd_data']
            if not (df['ytd_data'].empty or df['ytd_data'].isna().all()): # 判断 YTD 列是否有有效数据
                _df = df[['Time', 'Data', 'ytd_data', 'UNION_ATTR']].reset_index(drop=True)
            else:
                _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
        else:
            _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    else:
        _df = df[['Time', 'Data', 'UNION_ATTR']].reset_index(drop=True)
    return _df


def long_seq_pred(df: pd.DataFrame, _df: pd.DataFrame, image_name: Union[None, str],
                  data_col: str, pred_version: int, params: dict):
    start_time = time.time()
    # 4. 长时序：可参考性和线性外推检测
    log.info(f"[长时序检测] 指标: {data_col}, 维度: {image_name}")
    reference_param = params_for_crd(data_col)
    reference_tag = reference_pipline_trend(_df, reference_param)
    line_trend_param = params_for_line_trend(data_col)
    line_tag = conditional_line_trend(_df, line_trend_param)

    log.info(
        f"[检测结果] 指标: {data_col}, 维度: {image_name}, 可参考性: {reference_tag}, 线性外推: {line_tag}")

    if reference_tag or line_tag:
        log.info(f"[规则预测] 指标: {data_col}, 维度: {image_name}, 满足规则预测条件")
        _df = ytd_add(df, data_col)
        pred, pi_pred, pi_dist, mid_data, msg = rule_profit_pipeline(_df, params)
        log.info(
            f"[规则预测完成] 指标: {data_col}, 维度: {image_name}, 耗时: {time.time() - start_time:.2f}秒")
    else:
        # 5. API预测流程
        log.info(f"[API预测开始] 指标: {data_col}, 维度: {image_name}, 不满足规则预测条件，启动API预测")
        api_start_time = time.time()

        # 异常检测预处理
        params_cad = params_for_cad(data_col)
        _ts = check_pipeline(_df, params_cad)

        # 调用API预测
        pred_pipred = tspred(_ts.data[['Time', 'Data']], data_col, pred_version, image_name)
        api_total_time = time.time() - api_start_time

        log.info(f"[API预测完成] 指标: {data_col}, 维度: {image_name}, API总耗时: {api_total_time:.2f}秒")

        # 6. 结果处理
        pred_col = ['Time', "y_pred"]
        pi_pred_col = ['Time', "y_pred_lower", "y_pred_upper"]

        # 将结果拆分为pred和pi_pred
        pred = pred_pipred[pred_col]
        pi_pred = pred_pipred[pi_pred_col]
        pred.rename(columns={"y_pred": "yhat"}, inplace=True)
        pi_pred.rename(columns={"y_pred_lower": "yhat_lower", "y_pred_upper": "yhat_upper"}, inplace=True)
        pi_dist, mid_data, msg = None, {"pi_confidence": 0.95, "eval_result": 0}, None
        log.info(f"[结果处理完成] 指标: {data_col}, 维度: {image_name}")

    return pred, pi_pred, pi_dist, mid_data, msg


def process_row_api(df: pd.DataFrame, period_col: str, pred_col: str,
                    metric_name: Union[None, str], pred_version: int, is_encrypt: bool = False) -> Tuple[pd.DataFrame, pd.DataFrame, np.ndarray, dict, str]:
    """
    :param df:
    :param period_col: 预测期次列
    :param pred_col: 预测指标列
    :param metric_name:
    :return: pred, pi_pred, pi_dist, mid_data, msg
    pred：点预测-两列
    pi_pred:区间预测-三列
    mid_data：字典{pi_confidence：’0.95‘，'eval_result'： 0 }
    """
    start_time = time.time()

    pred = None
    try:
        # 1. 数据验证
        if df.empty:
            log.warning(f"[数据为空] 指标: {pred_col}, 维度指标: {metric_name}")
            empty_pred = pd.DataFrame(columns=['Time', 'y_hat'])
            empty_pi_pred = pd.DataFrame(columns=['Time', 'yhat_upper', 'yhat_lower'])
            return empty_pred, empty_pi_pred, np.array([]), {}, "Missing dimensions"

        # 2. 数据预处理
        _df, fill_value = process_data(df, period_col, pred_col, metric_name)
        params = get_params_for_algo_sdk(pred_col, pred_version)
        # 3. 预测策略选择
        data_length = len(_df)
        log.info(f"[策略选择] 指标: {pred_col}, 维度指标: {metric_name}, 处理后数据长度: {data_length}")

        # 短时序or对价后收入 or 来源是密文数据
        if use_sdk_for_prediction(data_length, is_encrypt, pred_col):
            log.info(f"[algo_sdk时序预测] 指标: {pred_col}, 维度指标: {metric_name}")
            pred, pi_pred, pi_dist, mid_data, msg = rule_profit_pipeline(_df, params)
            log.info(f"[algo_sdk时序预测] 指标: {pred_col}, 维度指标: {metric_name}, 耗时: {time.time() - start_time:.2f}秒")
        else:  # 长时序
            pred, pi_pred, pi_dist, mid_data, msg = long_seq_pred(df, _df, metric_name, pred_col, pred_version, params)

    except Exception as e:
        error_time = time.time() - start_time
        log.error(f"[预测异常] 指标: {pred_col}, 维度指标: {metric_name}, 时序长度: {len(df)}, 异常: {str(e)}, 耗时: {error_time:.2f}秒")
        # 兜底处理
        log.info(f"[兜底处理开始] 指标: {pred_col}, 维度指标: {metric_name}")
        _df = ytd_add(df, pred_col)
        pred, pi_pred, pi_dist, mid_data, msg = rule_profit_pipeline(_df, params)

        # 保存异常信息
        _df['error'] = str(e)
        n_len = _df['Time'].nunique()
        exception_file_path = Path(__file__).parent.parent / "exception_file" / f"{pred_col}_{len(_df)}_{n_len}.csv"
        _df.to_csv(exception_file_path)

        log.info(f"[兜底处理完成] 指标: {pred_col}, 维度指标: {metric_name}, "
                 f"时序长度类型: {'Long' if n_len >= 11 else 'Short'}, 异常文件: {exception_file_path}")

    # 8. 最终处理
    if pred is not None:
        pred.fillna(fill_value, inplace=True)  # 兜底填充

    total_time = time.time() - start_time
    log.info(f"[预测完成] 指标: {pred_col}, 维度指标: {metric_name}, 总耗时: {total_time:.2f}秒, "
             f"预测点数: {len(pred) if pred is not None else 0}")

    return pred, pi_pred, pi_dist, mid_data, msg


def use_sdk_for_prediction(data_length: int, is_encrypt: bool, pred_col: str) -> bool:
    # 逃生开关
    escape_switch_enabled = os.environ.get(ESCAPE_SWITCH_ENABLED, "false") == "true"
    if escape_switch_enabled:
        return True
    return data_length < 11 or pred_col == 'equip_rev_cons_after_amt' or is_encrypt


def pred_result_deal(result_rows: list, mid_data: dict, pred: pd.DataFrame,
                     pi_pred: pd.DataFrame, period_col: str,
                     result_row: dict, pred_col: str, pi_dist: np.ndarray, msg: str):
    '''
    对预测结果后处理
    :param result_rows:
    :param mid_data: 置信水平（pi_confidence）和评估指标（eval_result）
    :param pred: 点预测结果 DataFrame，通常包含 Time 和 yhat 列
    :param pi_pred: 区间预测结果 DataFrame，通常包含 Time, yhat_lower, yhat_upper
    :param period_col: 时间列名（如 'target_period'）
    :param result_row:
    :param pred_col: 预测指标列名
    :param pi_dist: 预测区间分布信息（如分位数数组，类型为 np.ndarray）
    :param msg:
    :return:
    '''
    pi_confidence = mid_data.get('pi_confidence', 0.95)
    eval_result = mid_data.get('eval_result', 0)
    # 重置索引
    pred = pred.reset_index()
    pi_pred = pi_pred.reset_index()
    pi_pred.rename(columns={'Time': 'target_period'}, inplace=True)
    pred.rename(columns={'Time': 'target_period'}, inplace=True)
    # 合并点预测和区间预测结果， period_col（即 target_period）为键，左连接
    pred_ts = pred.merge(pi_pred, on=period_col, how='left')
    # 是否需要区间预测
    if pred_col in indicators_need_interval_prediction:
        if not pred_ts.empty:
            for _, time_row in pred_ts.iterrows():
                temp = result_row.copy()
                temp.update({
                    period_col: time_row[period_col],
                    f"{pred_col}_fcst": time_row['yhat'],
                    f"{pred_col}_fcst_upper": time_row.get('yhat_upper', None),
                    f"{pred_col}_fcst_lower": time_row.get('yhat_lower', None),
                    f"{pred_col}_pi_dist": pi_dist,
                    f"{pred_col}_fcst_conf": pi_confidence,
                    f"{pred_col}_fcst_acc": eval_result,
                    'msg': msg
                })
                result_rows.append(temp)
        else:
            # 处理空数据情况
            temp = result_row.copy()
            temp.update({
                period_col: None, f"{pred_col}_fcst": None, f"{pred_col}_fcst_upper": None,
                f"{pred_col}_fcst_lower": None, f"{pred_col}_pi_dist": None, f"{pred_col}_fcst_conf": None,
                f"{pred_col}_fcst_acc": None, 'msg': None})
            result_rows.append(temp)
    else:
        if not pred_ts.empty:
            for _, time_row in pred_ts.iterrows():
                temp = result_row.copy()
                temp.update({period_col: time_row[period_col], f"{pred_col}_fcst": time_row['yhat'],
                             f"{pred_col}_pi_dist": pi_dist, 'msg': msg})
                result_rows.append(temp)
        else:
            temp = result_row.copy()
            temp.update({period_col: None, f"{pred_col}_fcst": None, f"{pred_col}_pi_dist": None, 'msg': None})
            result_rows.append(temp)


def process_dimension(
        dim_key: Tuple[Any],
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        sub_df: pd.DataFrame,
        pred_version: int,
        is_encrypt: bool = False
        ) -> List[Dict[str, Any]]:
    """
    处理单个维度组合的所有预测列
    """
    dimension_start_time = time.time()
    dim_name = '-'.join(map(str, dim_key))
    log.info(f"[维度处理开始] 维度: {dim_name}, 预测指标数: {len(pred_cols)}, 数据行数: {len(sub_df)}")
    # dim_key 是实际的维度成员组合
    # 样例： ('量纲子类', 'PDCG901159', '运营商', 'GH0001', '全球', '101775', '数据存储', '135229', '闪存存储', 'CNY',
    # 'S014065', 'Dorado 5000/5500', 'Dorado 5000/5500')
    # dim 是 对应顺序的数据库字段组合
    result_rows = []
    result_row = {d: v for d, v in zip(dim, dim_key)}  # 构建维度字段

    for idx, pred_col in enumerate(pred_cols, 1):
        col_start_time = time.time()
        log.info(f"[指标处理] 维度: {dim_name}, 进度: {idx}/{len(pred_cols)}, 当前指标: {pred_col}")
        try:
            # 调用预测函数
            pred, pi_pred, pi_dist, mid_data, msg = process_row_api(
                sub_df,
                period_col=period_col,
                pred_col=pred_col,
                metric_name='-'.join(map(str, dim_key)) + pred_col, pred_version=pred_version, is_encrypt=is_encrypt
            )
            # 将预测结果（包括点预测和区间预测）结构化并追加到结果
            pred_result_deal(result_rows, mid_data, pred, pi_pred, period_col, result_row, pred_col, pi_dist, msg)
        except Exception as e:
            log.error(f"处理 {dim_key} 的 {pred_col} 时出错: {e}")
            # 添加错误标记行
            temp = result_row.copy()
            temp.update({
                period_col: None,
                f"{pred_col}_fcst": None,
                f"{pred_col}_fcst_upper": None,
                f"{pred_col}_fcst_lower": None,
                f"{pred_col}_pi_dist": None,
                f"{pred_col}_fcst_conf": None,
                f"{pred_col}_fcst_acc": None,
                'msg': f"Error: {str(e)}"
            })
            result_rows.append(temp)

        col_time = time.time() - col_start_time
        log.info(f"[指标完成] 维度: {dim_name}, 进度: {idx}/{len(pred_cols)}, 指标: {pred_col}, 耗时: {col_time:.2f}秒")

    dimension_time = time.time() - dimension_start_time
    log.info(f"[维度处理完成] 维度: {dim_name}, 总耗时: {dimension_time:.2f}秒, 结果行数: {len(result_rows)}")

    return result_rows


def integrate_results(
        his_df: pd.DataFrame,
        dim: List[str],
        pred_cols: List[str],
        period_col: str,
        pred_version: int,
        is_encrypt: bool = False
) -> pd.DataFrame:
    """
    整合所有预测结果到 DataFrame
    """
    integrate_start_time = time.time()
    log.info(f"[整合开始] 输入数据shape: {his_df.shape}, 维度字段: {dim}, 预测指标: {pred_cols}")

    # 创建维度分组字典
    dim_dict = {tuple(k): v for k, v in his_df.groupby(dim)}
    dict_time = time.time()
    # 准备多进程任务参数
    tasks = []
    for dim_key, sub_df in dim_dict.items():
        tasks.append((dim_key, dim, pred_cols, period_col, sub_df, pred_version, is_encrypt))
    multipre_time = time.time()
    log.info(f"准备多进程任务参数: {multipre_time - dict_time:.5f}秒")

    log.info(f"[任务准备] 总任务数: {len(tasks)}, 每个任务包含 {len(pred_cols)} 个预测指标")

    # 多进程处理
    concurrent_num = os.environ.get('CHILD_PROCESS_NUM', 1)
    log.info(f"[多进程开始] 进程数: {concurrent_num}")

    process_start_time = time.time()

    with Pool(processes=int(concurrent_num)) as pool:
        results = pool.starmap(process_dimension, tasks)
    process_time = time.time() - process_start_time

    log.info(f"[多进程完成] 耗时: {process_time:.2f}秒")

    # 合并所有结果
    merge_start_time = time.time()
    main_result = pd.DataFrame([item for sublist in results for item in sublist])
    merge_time = time.time() - merge_start_time

    total_time = time.time() - integrate_start_time
    log.info(f"[整合完成] 结果shape: {main_result.shape}, 合并耗时: {merge_time:.2f}秒, 总耗时: {total_time:.2f}秒")

    return main_result


def is_monthly_continuous(df_train, df_test):
     # 确保日期列是 datetime 类型
    df = pd.concat([df_train, df_test], axis=0)
    date_col = "Time"
    # 提取年月（Period 类型）
    months = df[date_col].dt.to_period('M')
    # 获取最小和最大日期
    start = df[date_col].min()
    end = df[date_col].max()
    # 生成完整的月份序列（从 start 到 end，每月初）
    full_months = pd.date_range(start=start, end=end, freq='MS').to_period('M')
    # 检查集合是否一致
    return set(months) == set(full_months)


def get_ifs_api_params():
    """
    延迟初始化获取API配置参数，只在第一次调用时从环境变量读取
    """
    if not _ifs_api_params_cache:
        _ifs_api_params_cache.update({
            'problemId': int(os.environ.get('PROBLEM_ID')),
            'taskId': int(os.environ.get('TASK_ID')),
            'unit_cost_solutionId': int(os.environ.get('UNIT_COST_SOLUTIONID')),
            'unit_price_solutionId': int(os.environ.get('UNIT_PRICE_SOLUTIONID')),
            'other_param_solutionId': int(os.environ.get('OTHER_PARAM_SOLUTIONID'))
        })
        log.info(f"[API配置初始化] problemId: {_ifs_api_params_cache['problemId']}, "
                 f"taskId: {_ifs_api_params_cache['taskId']}")
    return _ifs_api_params_cache