{"DEPLOY": {"cloud": "HIS", "infra": "LOCAL", "enable_doc": true, "service_name": "", "uri_prefix": "/profit-risk-pred", "config_server": {"url": "https://appconfig-beta.huawei.com/ConfigCenter/services/saasConfigcenterGetConfig", "app_id": "com.huawei.fsppub", "du": "opt_fsp_pub_service", "environment": "kwe_sit", "region": "kwe", "version": "1.0", "config_parts": ["0F5A269030F614646BB2C413D3576DE8", "5639DFD1D094230C787C92034D6CBDED"]}, "api_gateway": {"authorization": {"url": "https://kwe-beta.huawei.com/ApiCommonQuery/appToken/getRestAppDynamicToken"}, "authentication": {"sgov_token": "https://kwe-beta.huawei.com/ApiCommonQuery/appToken/validateToken", "sgov_uri": "https://kwe-beta.huawei.com/ApiCommonQuery/appToken/authorizeByURI"}}}, "LOGGER": {"name": "<PERSON><PERSON>", "output_directory": null, "log_to_file": false, "log_format": "%(asctime)s | PID-%(process)d-%(threadName)s-%(thread)d | %(name)s | %(filename)s:%(lineno)d | %(levelname)s | %(message)s", "level": "DEBUG", "max_file_size": 102400000, "backup_count": 5}, "RDB": {"default": {"datasource_name": "fin_dm_opt_tod", "tables": ["databasechangelog"], "echo": true}}, "OBS": {"demo_bucket": {"bucket": "fai-mlops-demo", "pool_size": 5, "max_overflow": 5, "pool_timeout": 2, "pool_recycle": 3600, "pool_pre_ping": true}}, "LIMITER": {"storage_uri": null, "strategy": "moving-window", "unit": "minute", "frequency": 10}, "API": {"upload_url": "https://opt.hissit.huawei.com/opt/pc/ifs/v1/version/createVersionStartForecastByFile", "forecast_result_url": "https://opt.hissit.huawei.com/opt/pc/ifs/v1/version/generateTemporaryURL", "referer": "https://opt.hissit.huawei.com", "request_max_timeout": 10, "max_duration": 900, "interval": 10}}